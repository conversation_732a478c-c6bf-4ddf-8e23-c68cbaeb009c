import { useState, useEffect, useRef } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSSquarePayment component for processing Square card payments in POS
 *
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Object} props.orderDetails - Order details for Square
 * @returns {JSX.Element}
 */
export default function POSSquarePayment({
  amount,
  currency = 'AUD',
  onSuccess,
  onError,
  orderDetails = {}
}) {
  const [paymentForm, setPaymentForm] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [initializationAttempted, setInitializationAttempted] = useState(false)
  const containerRef = useRef(null)
  const mountedRef = useRef(false)

  // Enhanced React hydration-safe component lifecycle management
  useEffect(() => {
    mountedRef.current = true
    console.log('POSSquarePayment component mounted')

    // Wait for React hydration to complete before initializing Square SDK
    const initializeAfterHydration = () => {
      if (!mountedRef.current) return

      // Double-check that the container ref is available
      if (!containerRef.current) {
        console.log('Container ref not ready, retrying in 100ms...')
        setTimeout(initializeAfterHydration, 100)
        return
      }

      console.log('React hydration complete, container ready, loading Square SDK...')
      loadSquareSDK()
    }

    // Use multiple timing strategies for maximum compatibility
    if (typeof window !== 'undefined') {
      // Strategy 1: Wait for next tick to ensure React has finished rendering
      setTimeout(() => {
        // Strategy 2: Use requestAnimationFrame to wait for browser paint
        requestAnimationFrame(() => {
          // Strategy 3: Additional small delay for React hydration
          setTimeout(initializeAfterHydration, 50)
        })
      }, 0)
    }

    return () => {
      mountedRef.current = false
      console.log('POSSquarePayment component unmounting')
    }
  }, [])

  // Listen for force initialization events from debugger
  useEffect(() => {
    const handleForceInit = (event) => {
      console.log('🔄 Force initialization triggered by:', event.detail?.source)
      if (mountedRef.current && window.Square) {
        // Reset initialization state and try again
        setInitializationAttempted(false)
        setIsLoading(true)
        setErrorMessage('')

        // Small delay to ensure state updates
        setTimeout(() => {
          loadSquareSDK()
        }, 100)
      } else {
        console.warn('Cannot force init: mounted =', mountedRef.current, 'Square =', !!window.Square)
      }
    }

    window.addEventListener('forceSquareInit', handleForceInit)

    return () => {
      window.removeEventListener('forceSquareInit', handleForceInit)
    }
  }, [])

  const loadSquareSDK = () => {
    // Prevent multiple initialization attempts
    if (initializationAttempted) {
      console.log('Square SDK initialization already attempted')
      return
    }

    setInitializationAttempted(true)

    // Check if Square SDK is already loaded
    if (window.Square) {
      console.log('Square SDK already loaded, initializing payment form...')
      // Use requestAnimationFrame for better timing
      requestAnimationFrame(() => {
        if (mountedRef.current) {
          initializePaymentForm()
        }
      })
      return
    }

    console.log('Loading Square SDK...')

    // Load Square Web Payments SDK
    const script = document.createElement('script')
    script.src = 'https://sandbox.web.squarecdn.com/v1/square.js'
    script.async = true
    script.onload = () => {
      console.log('Square SDK loaded successfully')
      // Use requestAnimationFrame for better timing
      requestAnimationFrame(() => {
        if (mountedRef.current) {
          initializePaymentForm()
        }
      })
    }
    script.onerror = (error) => {
      console.error('Failed to load Square SDK:', error)
      if (mountedRef.current) {
        setIsLoading(false)
        setErrorMessage('Failed to load Square payment system. Please check your internet connection.')
        onError(new Error('Square SDK failed to load'))
      }
    }

    // Check if script already exists to avoid duplicates
    const existingScript = document.querySelector('script[src="https://sandbox.web.squarecdn.com/v1/square.js"]')
    if (!existingScript) {
      document.head.appendChild(script)
    } else {
      console.log('Square SDK script already exists, waiting for load...')
      // If script exists but Square isn't loaded yet, wait for it
      let attempts = 0
      const maxAttempts = 100 // 10 seconds

      const checkSquare = () => {
        attempts++

        // Check if component is still mounted before continuing
        if (!mountedRef.current) {
          console.log('Component unmounted, stopping Square SDK detection')
          return
        }

        if (window.Square) {
          console.log(`Square SDK detected after ${attempts} attempts`)
          if (mountedRef.current) {
            requestAnimationFrame(() => initializePaymentForm())
          }
          return
        }

        if (attempts >= maxAttempts) {
          console.error('Square SDK failed to load within timeout period')
          if (mountedRef.current) {
            setIsLoading(false)
            setErrorMessage('Square SDK failed to load within timeout period.')
            onError(new Error('Square SDK load timeout'))
          }
          return
        }

        // Continue checking only if component is still mounted
        if (mountedRef.current) {
          setTimeout(checkSquare, 100)
        }
      }

      checkSquare()
    }
  }

  const initializePaymentForm = async () => {
    console.log('initializePaymentForm called')

    // Check if component is still mounted using ref instead of state
    if (!mountedRef.current) {
      console.log('Component unmounted, skipping initialization')
      return
    }

    if (!window.Square) {
      console.error('Square SDK not available')
      if (mountedRef.current) {
        setErrorMessage('Square payment system not available')
        setIsLoading(false)
      }
      return
    }

    try {
      // Simplified and reliable DOM element detection
      console.log('Checking for Square container element...')

      // Use containerRef directly - it's the most reliable approach
      if (!containerRef.current) {
        throw new Error('Square payment container not available. Component may not be properly mounted.')
      }

      console.log('Square container element found via ref:', containerRef.current)

      // Validate environment variables
      const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
      const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

      if (!appId || !locationId) {
        throw new Error(`Missing Square configuration: appId=${!!appId}, locationId=${!!locationId}`)
      }

      console.log('Initializing Square payments with:', { appId, locationId })

      // Initialize Square payments
      const payments = window.Square.payments(appId, locationId)

      // Create card payment method with valid Square SDK styling
      const card = await payments.card({
        style: {
          '.input-container': {
            borderColor: '#e0e0e0',
            borderRadius: '8px'
          },
          '.input-container.is-focus': {
            borderColor: '#4ECDC4'
          },
          '.input-container.is-error': {
            borderColor: '#dc3545'
          },
          '.message-text': {
            color: '#dc3545'
          }
        }
      })

      console.log('Square card object created, attempting to attach...')

      // Attach card form to container
      await card.attach('#pos-square-card-container')

      console.log('Square card form attached successfully!')
      setPaymentForm(card)
      setIsLoading(false)
      setErrorMessage('')

    } catch (error) {
      console.error('Error initializing Square payment form:', error)

      // Only update state if component is still mounted
      if (mountedRef.current) {
        setIsLoading(false)

        // Provide user-friendly error messages
        let userMessage = 'Failed to initialize payment form'

        if (error.message.includes('container not available')) {
          userMessage = 'Payment form container not ready. Please try again.'
        } else if (error.message.includes('Square configuration')) {
          userMessage = 'Payment system configuration error. Please contact support.'
        } else if (error.message.includes('InvalidStylesError')) {
          userMessage = 'Payment form styling error. Retrying with default styles...'
          // Attempt retry with no custom styles
          setTimeout(() => {
            if (mountedRef.current) {
              setInitializationAttempted(false)
              loadSquareSDK()
            }
          }, 1000)
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          userMessage = 'Network error. Please check your connection and try again.'
        }

        setErrorMessage(userMessage)
        onError(error)
      } else {
        console.log('Component unmounted, skipping error state update')
      }
    }
  }

  const handlePayment = async () => {
    if (!paymentForm) {
      setErrorMessage('Payment form not ready')
      return
    }

    try {
      setIsProcessing(true)
      setErrorMessage('')

      // Tokenize payment method
      const result = await paymentForm.tokenize()

      if (result.status === 'OK') {
        // Process payment with the token
        const paymentResponse = await processPayment(result.token)

        if (paymentResponse.success) {
          onSuccess({
            paymentId: paymentResponse.paymentId,
            paymentStatus: 'COMPLETED',
            paymentDetails: {
              token: result.token,
              amount: amount,
              currency: currency,
              transactionId: paymentResponse.transactionId
            }
          })
        } else {
          throw new Error(paymentResponse.error || 'Payment processing failed')
        }
      } else {
        const errorMsg = result.errors?.[0]?.message || 'Payment tokenization failed'
        setErrorMessage(errorMsg)
        onError(new Error(errorMsg))
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      setErrorMessage(error.message || 'Payment failed. Please try again.')
      onError(error)
    } finally {
      setIsProcessing(false)
    }
  }

  const processPayment = async (token) => {
    try {
      // Get authentication token from Supabase session
      const { supabase } = await import('@/lib/supabase')
      const { data: { session } } = await supabase.auth.getSession()

      if (!session?.access_token) {
        throw new Error('Authentication required. Please log in to process payments.')
      }

      const response = await fetch('/api/admin/pos/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          token,
          amount,
          currency,
          orderDetails
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Payment processing failed')
      }

      return data
    } catch (error) {
      console.error('Payment API error:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  if (isLoading) {
    return (
      <div className={styles.squarePaymentContainer}>
        <div className={styles.loading}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading payment form...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.squarePaymentContainer}>
      <div className={styles.paymentFormHeader}>
        <h4>Card Payment</h4>
        <div className={styles.paymentAmount}>
          Amount: <span>${parseFloat(amount || 0).toFixed(2)} {currency}</span>
        </div>
      </div>

      {errorMessage && (
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>{errorMessage}</div>
            <button
              onClick={() => {
                setErrorMessage('')
                setInitializationAttempted(false)
                setIsLoading(true)
                loadSquareSDK()
              }}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className={styles.cardFormContainer}>
        <div
          id="pos-square-card-container"
          ref={containerRef}
          className={styles.cardForm}
          data-testid="square-card-container"
        >
          {/* Square card form will be injected here */}
          {isLoading && (
            <div className={styles.cardFormPlaceholder}>
              <p>Initializing secure payment form...</p>
            </div>
          )}
        </div>
      </div>

      <div className={styles.paymentActions}>
        <button
          onClick={handlePayment}
          disabled={isProcessing || !paymentForm}
          className={styles.processPaymentButton}
        >
          {isProcessing ? (
            <>
              <div className={styles.buttonSpinner}></div>
              Processing...
            </>
          ) : (
            `Charge $${parseFloat(amount || 0).toFixed(2)}`
          )}
        </button>
      </div>

      <div className={styles.paymentSecurity}>
        <div className={styles.securityBadges}>
          <span className={styles.securityBadge}>🔒 SSL Encrypted</span>
          <span className={styles.securityBadge}>✅ PCI Compliant</span>
          <span className={styles.securityBadge}>🛡️ Square Secure</span>
        </div>
        <p className={styles.securityText}>
          Your payment information is processed securely by Square and never stored on our servers.
        </p>
      </div>
    </div>
  )
}
