import { useState, useEffect } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSSquarePayment component for processing Square card payments in POS
 *
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Object} props.orderDetails - Order details for Square
 * @returns {JSX.Element}
 */
export default function POSSquarePayment({
  amount,
  currency = 'AUD',
  onSuccess,
  onError,
  orderDetails = {}
}) {
  const [paymentForm, setPaymentForm] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')

  // Initialize Square payment form
  useEffect(() => {
    loadSquareSDK()
  }, [])

  const loadSquareSDK = () => {
    // Check if Square SDK is already loaded
    if (window.Square) {
      console.log('Square SDK already loaded, initializing payment form...')
      // Add a small delay to ensure DOM is ready
      setTimeout(initializePaymentForm, 100)
      return
    }

    console.log('Loading Square SDK...')

    // Load Square Web Payments SDK
    const script = document.createElement('script')
    script.src = 'https://sandbox.web.squarecdn.com/v1/square.js'
    script.async = true
    script.onload = () => {
      console.log('Square SDK loaded successfully')
      // Add a small delay to ensure everything is ready
      setTimeout(initializePaymentForm, 100)
    }
    script.onerror = (error) => {
      console.error('Failed to load Square SDK:', error)
      setIsLoading(false)
      setErrorMessage('Failed to load Square payment system. Please check your internet connection.')
      onError(new Error('Square SDK failed to load'))
    }

    // Check if script already exists to avoid duplicates
    const existingScript = document.querySelector('script[src="https://sandbox.web.squarecdn.com/v1/square.js"]')
    if (!existingScript) {
      document.head.appendChild(script)
    } else {
      console.log('Square SDK script already exists, waiting for load...')
      // If script exists but Square isn't loaded yet, wait for it
      const checkSquare = setInterval(() => {
        if (window.Square) {
          clearInterval(checkSquare)
          setTimeout(initializePaymentForm, 100)
        }
      }, 100)

      // Timeout after 10 seconds
      setTimeout(() => {
        clearInterval(checkSquare)
        if (!window.Square) {
          setIsLoading(false)
          setErrorMessage('Square SDK failed to load within timeout period.')
          onError(new Error('Square SDK load timeout'))
        }
      }, 10000)
    }

    return () => {
      // Cleanup function
      const script = document.querySelector('script[src="https://sandbox.web.squarecdn.com/v1/square.js"]')
      if (script && document.head.contains(script)) {
        document.head.removeChild(script)
      }
    }
  }

  const initializePaymentForm = async () => {
    if (!window.Square) {
      setErrorMessage('Square payment system not available')
      setIsLoading(false)
      return
    }

    try {
      // Wait for DOM element to be available
      const waitForElement = (selector, timeout = 5000) => {
        return new Promise((resolve, reject) => {
          const element = document.querySelector(selector)
          if (element) {
            resolve(element)
            return
          }

          const observer = new MutationObserver(() => {
            const element = document.querySelector(selector)
            if (element) {
              observer.disconnect()
              resolve(element)
            }
          })

          observer.observe(document.body, {
            childList: true,
            subtree: true
          })

          setTimeout(() => {
            observer.disconnect()
            reject(new Error(`Element ${selector} not found within ${timeout}ms`))
          }, timeout)
        })
      }

      // Wait for the container element to be available
      await waitForElement('#pos-square-card-container')

      // Initialize Square with your application ID and location ID
      const payments = window.Square.payments(
        process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID,
        process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
      )

      console.log('Square payments initialized with:', {
        appId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID,
        locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
      })

      // Create card payment method
      const card = await payments.card({
        style: {
          '.input-container': {
            borderColor: '#e0e0e0',
            borderRadius: '8px'
          },
          '.input-container.is-focus': {
            borderColor: '#4ECDC4'
          },
          '.input-container.is-error': {
            borderColor: '#dc3545'
          },
          '.message-text': {
            color: '#dc3545'
          }
        }
      })

      // Attach card form to container
      await card.attach('#pos-square-card-container')

      setPaymentForm(card)
      setIsLoading(false)
      console.log('Square card form attached successfully')
    } catch (error) {
      console.error('Error initializing Square payment form:', error)
      setIsLoading(false)
      setErrorMessage(`Failed to initialize payment form: ${error.message}`)
      onError(error)
    }
  }

  const handlePayment = async () => {
    if (!paymentForm) {
      setErrorMessage('Payment form not ready')
      return
    }

    try {
      setIsProcessing(true)
      setErrorMessage('')

      // Tokenize payment method
      const result = await paymentForm.tokenize()

      if (result.status === 'OK') {
        // Process payment with the token
        const paymentResponse = await processPayment(result.token)

        if (paymentResponse.success) {
          onSuccess({
            paymentId: paymentResponse.paymentId,
            paymentStatus: 'COMPLETED',
            paymentDetails: {
              token: result.token,
              amount: amount,
              currency: currency,
              transactionId: paymentResponse.transactionId
            }
          })
        } else {
          throw new Error(paymentResponse.error || 'Payment processing failed')
        }
      } else {
        const errorMsg = result.errors?.[0]?.message || 'Payment tokenization failed'
        setErrorMessage(errorMsg)
        onError(new Error(errorMsg))
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      setErrorMessage(error.message || 'Payment failed. Please try again.')
      onError(error)
    } finally {
      setIsProcessing(false)
    }
  }

  const processPayment = async (token) => {
    try {
      const response = await fetch('/api/admin/pos/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          amount,
          currency,
          orderDetails
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Payment processing failed')
      }

      return data
    } catch (error) {
      console.error('Payment API error:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  if (isLoading) {
    return (
      <div className={styles.squarePaymentContainer}>
        <div className={styles.loading}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading payment form...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.squarePaymentContainer}>
      <div className={styles.paymentFormHeader}>
        <h4>Card Payment</h4>
        <div className={styles.paymentAmount}>
          Amount: <span>${parseFloat(amount || 0).toFixed(2)} {currency}</span>
        </div>
      </div>

      {errorMessage && (
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          {errorMessage}
        </div>
      )}

      <div className={styles.cardFormContainer}>
        <div id="pos-square-card-container" className={styles.cardForm}>
          {/* Square card form will be injected here */}
        </div>
      </div>

      <div className={styles.paymentActions}>
        <button
          onClick={handlePayment}
          disabled={isProcessing || !paymentForm}
          className={styles.processPaymentButton}
        >
          {isProcessing ? (
            <>
              <div className={styles.buttonSpinner}></div>
              Processing...
            </>
          ) : (
            `Charge $${parseFloat(amount || 0).toFixed(2)}`
          )}
        </button>
      </div>

      <div className={styles.paymentSecurity}>
        <div className={styles.securityBadges}>
          <span className={styles.securityBadge}>🔒 SSL Encrypted</span>
          <span className={styles.securityBadge}>✅ PCI Compliant</span>
          <span className={styles.securityBadge}>🛡️ Square Secure</span>
        </div>
        <p className={styles.securityText}>
          Your payment information is processed securely by Square and never stored on our servers.
        </p>
      </div>
    </div>
  )
}
