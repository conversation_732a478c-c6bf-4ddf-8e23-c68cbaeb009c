# Square Payment Testing Guide for POS Terminal

## 🔧 Environment Setup

### Required Environment Variables in `.env.local`:

```env
# Square Sandbox Configuration
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-YOUR_APP_ID
NEXT_PUBLIC_SQUARE_LOCATION_ID=YOUR_LOCATION_ID
SQUARE_ACCESS_TOKEN=YOUR_SANDBOX_ACCESS_TOKEN
SQUARE_ENVIRONMENT=sandbox
```

**Important Notes:**
- Use `NEXT_PUBLIC_` prefix for client-side variables (Application ID, Location ID)
- Keep `SQUARE_ACCESS_TOKEN` without prefix (server-side only)
- Always use `sandbox` environment for testing

## 🧪 Testing Steps

### 1. **Access the POS Terminal**
- Navigate to `http://localhost:3000/admin/pos`
- Login with admin credentials if prompted

### 2. **Complete the POS Flow**
- **Step 1**: Select any service from the grid
- **Step 2**: Choose a pricing tier
- **Step 3**: Enter customer information (or skip)
- **Step 4**: Select "Card Payment"

### 3. **Square Payment Form Testing**

#### **Test Card Numbers (Sandbox Only):**

**Successful Payments:**
- `4111 1111 1111 1111` (Visa)
- `5555 5555 5555 4444` (Mastercard)
- `3782 822463 10005` (American Express)

**Failed Payments (for testing error handling):**
- `4000 0000 0000 0002` (Card Declined)
- `4000 0000 0000 9995` (Insufficient Funds)
- `4000 0000 0000 0069` (Expired Card)

#### **Test Details:**
- **CVV**: Any 3-4 digit number (e.g., `123`)
- **Expiry**: Any future date (e.g., `12/25`)
- **ZIP**: Any 5-digit code (e.g., `12345`)

## 🐛 Debugging the DOM Element Issue

### **Console Debugging:**
Open browser DevTools (F12) and check for these logs:

1. **Square SDK Loading:**
   ```
   Loading Square SDK...
   Square SDK loaded successfully
   ```

2. **Payment Form Initialization:**
   ```
   Square SDK already loaded, initializing payment form...
   Square payments initialized with: {appId: "...", locationId: "..."}
   Square card form attached successfully
   ```

3. **DOM Element Check:**
   ```javascript
   // Run this in browser console to verify element exists:
   document.querySelector('#pos-square-card-container')
   ```

### **Common Issues & Solutions:**

#### **Issue 1: "Element #pos-square-card-container was not found"**
**Solution**: The DOM timing fix should resolve this. If it persists:
1. Check browser console for the element existence
2. Verify the component is rendering properly
3. Look for React rendering errors

#### **Issue 2: "Square SDK failed to load"**
**Solution**: 
1. Check internet connection
2. Verify no ad blockers are blocking Square scripts
3. Check browser console for network errors

#### **Issue 3: "Payment system not configured"**
**Solution**:
1. Verify all environment variables are set correctly
2. Restart the development server after adding env vars
3. Check the API logs for configuration details

## 🔍 Testing Checklist

### **Before Testing:**
- [ ] Environment variables are set in `.env.local`
- [ ] Development server restarted after env changes
- [ ] Browser DevTools open to monitor console logs
- [ ] Admin authentication working

### **During Testing:**
- [ ] Service selection works
- [ ] Tier selection works  
- [ ] Customer form submission works
- [ ] Payment method selection shows card option
- [ ] Square payment form loads without errors
- [ ] Test card numbers can be entered
- [ ] Payment processing completes successfully

### **After Successful Payment:**
- [ ] Booking created in admin bookings section
- [ ] Payment recorded in admin payments section
- [ ] Customer added to customer database
- [ ] Console shows successful payment logs

## 🚨 Troubleshooting

### **If Square Form Still Won't Load:**

1. **Check Network Tab** in DevTools:
   - Look for failed requests to `squarecdn.com`
   - Verify Square SDK script loads successfully

2. **Check Console Errors**:
   - Look for JavaScript errors
   - Check for React component errors

3. **Verify Environment Variables**:
   ```javascript
   // Run in browser console:
   console.log({
     appId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID,
     locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
   })
   ```

4. **Test API Endpoint Directly**:
   ```bash
   curl -X POST http://localhost:3000/api/admin/pos/process-payment \
     -H "Content-Type: application/json" \
     -d '{"token":"test","amount":10,"currency":"AUD"}'
   ```

### **If Payment Processing Fails:**

1. **Check Server Logs** for Square API errors
2. **Verify Access Token** has correct permissions
3. **Check Location ID** matches your Square account
4. **Test with different card numbers**

## 📞 Support

If you continue to experience issues:

1. **Share Console Logs**: Copy any error messages from browser console
2. **Share Server Logs**: Check terminal output for API errors  
3. **Verify Square Account**: Ensure sandbox account is properly configured
4. **Test Environment**: Try in incognito/private browsing mode

## 🎯 Expected Behavior

**Successful Flow:**
1. Square SDK loads → Console: "Square SDK loaded successfully"
2. Payment form attaches → Console: "Square card form attached successfully"  
3. Card details entered → Form validates input
4. Payment submitted → API processes payment
5. Success response → Booking and payment created
6. POS resets → Ready for next transaction

The fixes I've implemented should resolve the DOM timing issue and provide proper error handling throughout the payment flow.
