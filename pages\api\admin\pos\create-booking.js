import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for creating POS bookings with customer and payment data
 * This endpoint handles the complete POS transaction flow
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] POS Create Booking API called: ${req.method}`)

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Authenticate request
  const authResult = await authenticateAdminRequest(req)
  const { authorized, error, user, role } = authResult

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error')
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    })
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`)

  try {
    const { service, tier, customer, payment } = req.body

    // Validate required data
    if (!service?.id || !tier?.id || !customer || !payment) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'Service, tier, customer, and payment information are required'
      })
    }

    console.log(`[${requestId}] Creating POS booking for service: ${service.name}`)

    // Start a transaction-like process
    let customerId = null
    let bookingId = null
    let paymentId = null

    try {
      // Step 1: Create or find customer
      if (customer.isAnonymous) {
        // For anonymous customers, create a minimal record
        const { data: customerData, error: customerError } = await supabaseAdmin
          .from('customers')
          .insert([{
            name: customer.name || 'Walk-in Customer',
            email: null,
            phone: null,
            marketing_consent: false,
            notes: 'POS walk-in customer'
          }])
          .select()
          .single()

        if (customerError) throw customerError
        customerId = customerData.id
        console.log(`[${requestId}] Created anonymous customer: ${customerId}`)
      } else {
        // For named customers, create or update record
        const { data: existingCustomer } = await supabaseAdmin
          .from('customers')
          .select('id')
          .eq('email', customer.email)
          .single()

        if (existingCustomer) {
          customerId = existingCustomer.id
          console.log(`[${requestId}] Using existing customer: ${customerId}`)
        } else {
          const { data: customerData, error: customerError } = await supabaseAdmin
            .from('customers')
            .insert([{
              name: customer.name,
              email: customer.email || null,
              phone: customer.phone || null,
              marketing_consent: customer.marketingConsent || false,
              notes: 'Created via POS terminal'
            }])
            .select()
            .single()

          if (customerError) throw customerError
          customerId = customerData.id
          console.log(`[${requestId}] Created new customer: ${customerId}`)
        }
      }

      // Step 2: Create booking
      const startTime = new Date()
      const endTime = new Date(startTime.getTime() + (tier.duration * 60 * 1000))

      const { data: bookingData, error: bookingError } = await supabaseAdmin
        .from('bookings')
        .insert([{
          customer_id: customerId,
          service_id: service.id,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          status: 'confirmed', // POS bookings are immediately confirmed
          location: 'Festival/Event Location',
          notes: `POS booking - ${tier.name} tier - ${payment.method} payment`
        }])
        .select()
        .single()

      if (bookingError) throw bookingError
      bookingId = bookingData.id
      console.log(`[${requestId}] Created booking: ${bookingId}`)

      // Step 3: Create payment record
      const { data: paymentData, error: paymentError } = await supabaseAdmin
        .from('payments')
        .insert([{
          booking_id: bookingId,
          amount: payment.amount,
          currency: payment.currency || 'AUD',
          payment_method: payment.method,
          payment_status: 'completed',
          transaction_id: payment.details?.transactionId || null,
          payment_date: new Date().toISOString(),
          notes: `POS terminal payment - ${service.name} (${tier.name})`
        }])
        .select()
        .single()

      if (paymentError) throw paymentError
      paymentId = paymentData.id
      console.log(`[${requestId}] Created payment: ${paymentId}`)

      // Return success response
      return res.status(201).json({
        success: true,
        booking: {
          id: bookingId,
          customer_id: customerId,
          service_id: service.id,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          status: 'confirmed'
        },
        payment: {
          id: paymentId,
          amount: payment.amount,
          currency: payment.currency || 'AUD',
          method: payment.method,
          status: 'completed'
        },
        customer: {
          id: customerId,
          name: customer.name,
          email: customer.email || null
        },
        receipt: {
          service: service.name,
          tier: tier.name,
          duration: tier.duration,
          amount: payment.amount,
          currency: payment.currency || 'AUD',
          payment_method: payment.method,
          booking_time: startTime.toISOString(),
          booking_id: bookingId
        }
      })

    } catch (transactionError) {
      console.error(`[${requestId}] Transaction error:`, transactionError)
      
      // Attempt cleanup if we created partial records
      if (paymentId) {
        await supabaseAdmin.from('payments').delete().eq('id', paymentId)
      }
      if (bookingId) {
        await supabaseAdmin.from('bookings').delete().eq('id', bookingId)
      }
      if (customerId && customer.isAnonymous) {
        await supabaseAdmin.from('customers').delete().eq('id', customerId)
      }

      throw transactionError
    }

  } catch (error) {
    console.error(`[${requestId}] POS Booking Creation Error:`, error)

    // Determine appropriate status code
    let statusCode = 500
    let errorMessage = 'Failed to create booking'

    if (error.message && error.message.includes('validation')) {
      statusCode = 400
      errorMessage = 'Invalid booking data'
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404
      errorMessage = 'Service or tier not found'
    }

    return res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while creating the booking',
      requestId,
      timestamp: new Date().toISOString()
    })
  }
}
