import { useState, useEffect } from 'react'

/**
 * SquareDebugger component to help diagnose Square payment form issues
 * Also filters out browser extension errors that interfere with debugging
 */
export default function SquareDebugger() {
  const [debugInfo, setDebugInfo] = useState({})
  const [filteredErrors, setFilteredErrors] = useState([])

  // Filter out browser extension errors
  useEffect(() => {
    const originalConsoleError = console.error
    const originalConsoleWarn = console.warn

    const filterBrowserExtensionErrors = (originalMethod) => {
      return (...args) => {
        const message = args.join(' ')

        // Filter out known browser extension errors
        const extensionErrorPatterns = [
          'runtime.lastError',
          'message port closed',
          'Extension context invalidated',
          'chrome-extension://',
          'moz-extension://',
          'safari-extension://',
          'Could not establish connection'
        ]

        const isExtensionError = extensionErrorPatterns.some(pattern =>
          message.toLowerCase().includes(pattern.toLowerCase())
        )

        if (isExtensionError) {
          // Log filtered errors separately for debugging
          setFilteredErrors(prev => [...prev.slice(-4), {
            timestamp: new Date().toISOString(),
            message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
            type: 'extension'
          }])
          return // Don't log to console
        }

        // Log non-extension errors normally
        originalMethod.apply(console, args)
      }
    }

    console.error = filterBrowserExtensionErrors(originalConsoleError)
    console.warn = filterBrowserExtensionErrors(originalConsoleWarn)

    return () => {
      console.error = originalConsoleError
      console.warn = originalConsoleWarn
    }
  }, [])

  useEffect(() => {
    const updateDebugInfo = () => {
      const info = {
        timestamp: new Date().toISOString(),
        squareSDK: {
          loaded: !!window.Square,
          version: window.Square?.version || 'N/A'
        },
        environment: {
          appId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID || 'NOT SET',
          locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID || 'NOT SET',
          hasAccessToken: 'SERVER_SIDE_ONLY',
          environment: process.env.SQUARE_ENVIRONMENT || 'NOT SET',
          nodeEnv: process.env.NODE_ENV
        },
        errors: {
          filteredExtensionErrors: filteredErrors.length,
          recentFiltered: filteredErrors.slice(-2)
        },
        dom: {
          containerExists: !!document.querySelector('#pos-square-card-container'),
          containerVisible: (() => {
            const el = document.querySelector('#pos-square-card-container')
            if (!el) return false
            const rect = el.getBoundingClientRect()
            return rect.width > 0 && rect.height > 0
          })(),
          bodyChildren: document.body.children.length,
          scripts: Array.from(document.querySelectorAll('script')).map(s => s.src).filter(src => src.includes('square'))
        },
        browser: {
          userAgent: navigator.userAgent,
          cookiesEnabled: navigator.cookieEnabled,
          onLine: navigator.onLine
        }
      }
      setDebugInfo(info)
    }

    // Initial update
    updateDebugInfo()

    // Update every 2 seconds
    const interval = setInterval(updateDebugInfo, 2000)

    return () => clearInterval(interval)
  }, [])

  const handleTestElement = () => {
    const element = document.querySelector('#pos-square-card-container')
    console.log('Manual element test:', {
      element,
      exists: !!element,
      rect: element?.getBoundingClientRect(),
      styles: element ? window.getComputedStyle(element) : null
    })
  }

  const handleTestSquare = async () => {
    if (!window.Square) {
      console.error('Square SDK not loaded')
      return
    }

    try {
      console.log('Testing Square SDK initialization...')
      const payments = window.Square.payments(
        process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID,
        process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
      )
      console.log('✅ Square payments object created successfully:', payments)

      const card = await payments.card()
      console.log('✅ Square card object created successfully:', card)

      // Test attachment without actually attaching
      console.log('✅ Square SDK test completed successfully')
    } catch (error) {
      console.error('❌ Square test error:', error)
    }
  }

  const handleForceInitialization = () => {
    console.log('🔄 Forcing Square payment form initialization...')

    // Trigger a custom event that the POSSquarePayment component can listen to
    const event = new CustomEvent('forceSquareInit', {
      detail: { source: 'debugger' }
    })
    window.dispatchEvent(event)
  }

  if (process.env.NODE_ENV !== 'development') {
    return null // Only show in development
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'white',
      border: '2px solid #ccc',
      borderRadius: '8px',
      padding: '1rem',
      maxWidth: '400px',
      maxHeight: '80vh',
      overflow: 'auto',
      zIndex: 9999,
      fontSize: '12px',
      fontFamily: 'monospace'
    }}>
      <h4 style={{ margin: '0 0 1rem 0', color: '#333' }}>Square Debug Info</h4>

      <div style={{ marginBottom: '1rem' }}>
        <button onClick={handleTestElement} style={{ marginRight: '0.5rem', padding: '0.25rem 0.5rem', fontSize: '11px' }}>
          Test Element
        </button>
        <button onClick={handleTestSquare} style={{ marginRight: '0.5rem', padding: '0.25rem 0.5rem', fontSize: '11px' }}>
          Test Square
        </button>
        <button onClick={handleForceInitialization} style={{ padding: '0.25rem 0.5rem', fontSize: '11px', backgroundColor: '#4ECDC4', color: 'white', border: 'none', borderRadius: '3px' }}>
          Force Init
        </button>
      </div>

      <pre style={{
        background: '#f5f5f5',
        padding: '0.5rem',
        borderRadius: '4px',
        overflow: 'auto',
        maxHeight: '300px',
        margin: 0,
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-word'
      }}>
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
    </div>
  )
}
