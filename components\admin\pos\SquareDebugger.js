import { useState, useEffect } from 'react'

/**
 * SquareDebugger component to help diagnose Square payment form issues
 */
export default function SquareDebugger() {
  const [debugInfo, setDebugInfo] = useState({})

  useEffect(() => {
    const updateDebugInfo = () => {
      const info = {
        timestamp: new Date().toISOString(),
        squareSDK: {
          loaded: !!window.Square,
          version: window.Square?.version || 'N/A'
        },
        environment: {
          appId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID || 'NOT SET',
          locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID || 'NOT SET',
          hasAccessToken: !!process.env.SQUARE_ACCESS_TOKEN,
          environment: process.env.SQUARE_ENVIRONMENT || 'NOT SET'
        },
        dom: {
          containerExists: !!document.querySelector('#pos-square-card-container'),
          containerVisible: (() => {
            const el = document.querySelector('#pos-square-card-container')
            if (!el) return false
            const rect = el.getBoundingClientRect()
            return rect.width > 0 && rect.height > 0
          })(),
          bodyChildren: document.body.children.length,
          scripts: Array.from(document.querySelectorAll('script')).map(s => s.src).filter(src => src.includes('square'))
        },
        browser: {
          userAgent: navigator.userAgent,
          cookiesEnabled: navigator.cookieEnabled,
          onLine: navigator.onLine
        }
      }
      setDebugInfo(info)
    }

    // Initial update
    updateDebugInfo()

    // Update every 2 seconds
    const interval = setInterval(updateDebugInfo, 2000)

    return () => clearInterval(interval)
  }, [])

  const handleTestElement = () => {
    const element = document.querySelector('#pos-square-card-container')
    console.log('Manual element test:', {
      element,
      exists: !!element,
      rect: element?.getBoundingClientRect(),
      styles: element ? window.getComputedStyle(element) : null
    })
  }

  const handleTestSquare = async () => {
    if (!window.Square) {
      console.error('Square SDK not loaded')
      return
    }

    try {
      const payments = window.Square.payments(
        process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID,
        process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
      )
      console.log('Square payments object created:', payments)
      
      const card = await payments.card()
      console.log('Square card object created:', card)
    } catch (error) {
      console.error('Square test error:', error)
    }
  }

  if (process.env.NODE_ENV !== 'development') {
    return null // Only show in development
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'white',
      border: '2px solid #ccc',
      borderRadius: '8px',
      padding: '1rem',
      maxWidth: '400px',
      maxHeight: '80vh',
      overflow: 'auto',
      zIndex: 9999,
      fontSize: '12px',
      fontFamily: 'monospace'
    }}>
      <h4 style={{ margin: '0 0 1rem 0', color: '#333' }}>Square Debug Info</h4>
      
      <div style={{ marginBottom: '1rem' }}>
        <button onClick={handleTestElement} style={{ marginRight: '0.5rem', padding: '0.25rem 0.5rem' }}>
          Test Element
        </button>
        <button onClick={handleTestSquare} style={{ padding: '0.25rem 0.5rem' }}>
          Test Square
        </button>
      </div>

      <pre style={{ 
        background: '#f5f5f5', 
        padding: '0.5rem', 
        borderRadius: '4px',
        overflow: 'auto',
        maxHeight: '300px',
        margin: 0,
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-word'
      }}>
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
    </div>
  )
}
