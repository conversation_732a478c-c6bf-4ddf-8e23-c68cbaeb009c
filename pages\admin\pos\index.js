import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import ServiceTileGrid from '@/components/admin/pos/ServiceTileGrid'
import ServiceTierSelector from '@/components/admin/pos/ServiceTierSelector'
import POSCheckout from '@/components/admin/pos/POSCheckout'
import SquareDebugger from '@/components/admin/pos/SquareDebugger'
import { supabase } from '@/lib/supabase'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

export default function POSTerminal() {
  const [currentStep, setCurrentStep] = useState('services') // 'services', 'tiers', 'checkout'
  const [selectedService, setSelectedService] = useState(null)
  const [selectedTier, setSelectedTier] = useState(null)
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch services with pricing tiers
  useEffect(() => {
    fetchServices()
  }, [])

  const fetchServices = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch services with pricing tiers using the view
      const { data, error } = await supabase
        .from('services_with_pricing')
        .select('*')
        .eq('status', 'active')
        .order('name')

      if (error) {
        throw error
      }

      setServices(data || [])
    } catch (err) {
      console.error('Error fetching services:', err)
      setError('Failed to load services. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleServiceSelect = (service) => {
    setSelectedService(service)
    setSelectedTier(null)
    setCurrentStep('tiers')
  }

  const handleTierSelect = (tier) => {
    setSelectedTier(tier)
    setCurrentStep('checkout')
  }

  const handleBackToServices = () => {
    setSelectedService(null)
    setSelectedTier(null)
    setCurrentStep('services')
  }

  const handleBackToTiers = () => {
    setSelectedTier(null)
    setCurrentStep('tiers')
  }

  const handleTransactionComplete = () => {
    // Reset to services view after successful transaction
    setSelectedService(null)
    setSelectedTier(null)
    setCurrentStep('services')
  }

  const renderStepIndicator = () => (
    <div className={styles.stepIndicator}>
      <div className={`${styles.step} ${currentStep === 'services' ? styles.active : ''}`}>
        <span className={styles.stepNumber}>1</span>
        <span className={styles.stepLabel}>Select Service</span>
      </div>
      <div className={`${styles.step} ${currentStep === 'tiers' ? styles.active : ''}`}>
        <span className={styles.stepNumber}>2</span>
        <span className={styles.stepLabel}>Choose Duration</span>
      </div>
      <div className={`${styles.step} ${currentStep === 'checkout' ? styles.active : ''}`}>
        <span className={styles.stepNumber}>3</span>
        <span className={styles.stepLabel}>Checkout</span>
      </div>
    </div>
  )

  if (loading) {
    return (
      <ProtectedRoute>
        <AdminLayout title="POS Terminal">
          <div className={styles.posContainer}>
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading services...</p>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <AdminLayout title="POS Terminal">
          <div className={styles.posContainer}>
            <div className={styles.error}>
              <h3>Error Loading POS Terminal</h3>
              <p>{error}</p>
              <button
                className={styles.retryButton}
                onClick={fetchServices}
              >
                Retry
              </button>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <AdminLayout title="POS Terminal">
        <div className={styles.posContainer}>
          <div className={styles.posHeader}>
            <h1 className={styles.posTitle}>Point of Sale Terminal</h1>
            <p className={styles.posSubtitle}>Festival & Event Service Booking</p>
            {renderStepIndicator()}
          </div>

          <div className={styles.posContent}>
            {currentStep === 'services' && (
              <ServiceTileGrid
                services={services}
                onServiceSelect={handleServiceSelect}
              />
            )}

            {currentStep === 'tiers' && selectedService && (
              <ServiceTierSelector
                service={selectedService}
                onTierSelect={handleTierSelect}
                onBack={handleBackToServices}
              />
            )}

            {currentStep === 'checkout' && selectedService && selectedTier && (
              <POSCheckout
                service={selectedService}
                tier={selectedTier}
                onBack={handleBackToTiers}
                onComplete={handleTransactionComplete}
              />
            )}
          </div>

          {/* Debug component for development */}
          {process.env.NODE_ENV === 'development' && <SquareDebugger />}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
